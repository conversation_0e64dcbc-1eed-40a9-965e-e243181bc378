import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:crypto/crypto.dart';
import 'package:pointycastle/export.dart';
import 'package:sqflite/sqflite.dart';

import 'database_service.dart';
import '../utils/group_util.dart';

/// 群组加密密钥服务
/// 负责管理群组的加密密钥生成、存储和获取
class GroupEncryptionKeyService {
  GroupEncryptionKeyService._();

  // 16个公共群组的固定密钥（每个32字节）
  static final Map<String, Uint8List> _publicGroupKeys = {
    '10000001': Uint8List.fromList([
      0x01,
      0x23,
      0x45,
      0x67,
      0x89,
      0xAB,
      0xCD,
      0xEF,
      0xFE,
      0xDC,
      0xBA,
      0x98,
      0x76,
      0x54,
      0x32,
      0x10,
      0x11,
      0x22,
      0x33,
      0x44,
      0x55,
      0x66,
      0x77,
      0x88,
      0x99,
      0xAA,
      0xBB,
      0xCC,
      0xDD,
      0xEE,
      0xFF,
      0x00,
    ]),
    '10000002': Uint8List.fromList([
      0x02,
      0x34,
      0x56,
      0x78,
      0x9A,
      0xBC,
      0xDE,
      0xF0,
      0x0F,
      0xED,
      0xCB,
      0xA9,
      0x87,
      0x65,
      0x43,
      0x21,
      0x12,
      0x23,
      0x34,
      0x45,
      0x56,
      0x67,
      0x78,
      0x89,
      0x9A,
      0xAB,
      0xBC,
      0xCD,
      0xDE,
      0xEF,
      0xF0,
      0x01,
    ]),
    '10000003': Uint8List.fromList([
      0x03,
      0x45,
      0x67,
      0x89,
      0xAB,
      0xCD,
      0xEF,
      0x01,
      0x10,
      0xFE,
      0xDC,
      0xBA,
      0x98,
      0x76,
      0x54,
      0x32,
      0x13,
      0x24,
      0x35,
      0x46,
      0x57,
      0x68,
      0x79,
      0x8A,
      0x9B,
      0xAC,
      0xBD,
      0xCE,
      0xDF,
      0xE0,
      0xF1,
      0x02,
    ]),
    '10000004': Uint8List.fromList([
      0x04,
      0x56,
      0x78,
      0x9A,
      0xBC,
      0xDE,
      0xF0,
      0x12,
      0x21,
      0x0F,
      0xED,
      0xCB,
      0xA9,
      0x87,
      0x65,
      0x43,
      0x14,
      0x25,
      0x36,
      0x47,
      0x58,
      0x69,
      0x7A,
      0x8B,
      0x9C,
      0xAD,
      0xBE,
      0xCF,
      0xD0,
      0xE1,
      0xF2,
      0x03,
    ]),
    '10000005': Uint8List.fromList([
      0x05,
      0x67,
      0x89,
      0xAB,
      0xCD,
      0xEF,
      0x01,
      0x23,
      0x32,
      0x10,
      0xFE,
      0xDC,
      0xBA,
      0x98,
      0x76,
      0x54,
      0x15,
      0x26,
      0x37,
      0x48,
      0x59,
      0x6A,
      0x7B,
      0x8C,
      0x9D,
      0xAE,
      0xBF,
      0xC0,
      0xD1,
      0xE2,
      0xF3,
      0x04,
    ]),
    '10000006': Uint8List.fromList([
      0x06,
      0x78,
      0x9A,
      0xBC,
      0xDE,
      0xF0,
      0x12,
      0x34,
      0x43,
      0x21,
      0x0F,
      0xED,
      0xCB,
      0xA9,
      0x87,
      0x65,
      0x16,
      0x27,
      0x38,
      0x49,
      0x5A,
      0x6B,
      0x7C,
      0x8D,
      0x9E,
      0xAF,
      0xB0,
      0xC1,
      0xD2,
      0xE3,
      0xF4,
      0x05,
    ]),
    '10000007': Uint8List.fromList([
      0x07,
      0x89,
      0xAB,
      0xCD,
      0xEF,
      0x01,
      0x23,
      0x45,
      0x54,
      0x32,
      0x10,
      0xFE,
      0xDC,
      0xBA,
      0x98,
      0x76,
      0x17,
      0x28,
      0x39,
      0x4A,
      0x5B,
      0x6C,
      0x7D,
      0x8E,
      0x9F,
      0xA0,
      0xB1,
      0xC2,
      0xD3,
      0xE4,
      0xF5,
      0x06,
    ]),
    '10000008': Uint8List.fromList([
      0x08,
      0x9A,
      0xBC,
      0xDE,
      0xF0,
      0x12,
      0x34,
      0x56,
      0x65,
      0x43,
      0x21,
      0x0F,
      0xED,
      0xCB,
      0xA9,
      0x87,
      0x18,
      0x29,
      0x3A,
      0x4B,
      0x5C,
      0x6D,
      0x7E,
      0x8F,
      0x90,
      0xA1,
      0xB2,
      0xC3,
      0xD4,
      0xE5,
      0xF6,
      0x07,
    ]),
    '10000009': Uint8List.fromList([
      0x09,
      0xAB,
      0xCD,
      0xEF,
      0x01,
      0x23,
      0x45,
      0x67,
      0x76,
      0x54,
      0x32,
      0x10,
      0xFE,
      0xDC,
      0xBA,
      0x98,
      0x19,
      0x2A,
      0x3B,
      0x4C,
      0x5D,
      0x6E,
      0x7F,
      0x80,
      0x91,
      0xA2,
      0xB3,
      0xC4,
      0xD5,
      0xE6,
      0xF7,
      0x08,
    ]),
    '1000000A': Uint8List.fromList([
      0x0A,
      0xBC,
      0xDE,
      0xF0,
      0x12,
      0x34,
      0x56,
      0x78,
      0x87,
      0x65,
      0x43,
      0x21,
      0x0F,
      0xED,
      0xCB,
      0xA9,
      0x1A,
      0x2B,
      0x3C,
      0x4D,
      0x5E,
      0x6F,
      0x70,
      0x81,
      0x92,
      0xA3,
      0xB4,
      0xC5,
      0xD6,
      0xE7,
      0xF8,
      0x09,
    ]),
    '1000000B': Uint8List.fromList([
      0x0B,
      0xCD,
      0xEF,
      0x01,
      0x23,
      0x45,
      0x67,
      0x89,
      0x98,
      0x76,
      0x54,
      0x32,
      0x10,
      0xFE,
      0xDC,
      0xBA,
      0x1B,
      0x2C,
      0x3D,
      0x4E,
      0x5F,
      0x60,
      0x71,
      0x82,
      0x93,
      0xA4,
      0xB5,
      0xC6,
      0xD7,
      0xE8,
      0xF9,
      0x0A,
    ]),
    '1000000C': Uint8List.fromList([
      0x0C,
      0xDE,
      0xF0,
      0x12,
      0x34,
      0x56,
      0x78,
      0x9A,
      0xA9,
      0x87,
      0x65,
      0x43,
      0x21,
      0x0F,
      0xED,
      0xCB,
      0x1C,
      0x2D,
      0x3E,
      0x4F,
      0x50,
      0x61,
      0x72,
      0x83,
      0x94,
      0xA5,
      0xB6,
      0xC7,
      0xD8,
      0xE9,
      0xFA,
      0x0B,
    ]),
    '1000000D': Uint8List.fromList([
      0x0D,
      0xEF,
      0x01,
      0x23,
      0x45,
      0x67,
      0x89,
      0xAB,
      0xBA,
      0x98,
      0x76,
      0x54,
      0x32,
      0x10,
      0xFE,
      0xDC,
      0x1D,
      0x2E,
      0x3F,
      0x40,
      0x51,
      0x62,
      0x73,
      0x84,
      0x95,
      0xA6,
      0xB7,
      0xC8,
      0xD9,
      0xEA,
      0xFB,
      0x0C,
    ]),
    '1000000E': Uint8List.fromList([
      0x0E,
      0xF0,
      0x12,
      0x34,
      0x56,
      0x78,
      0x9A,
      0xBC,
      0xCB,
      0xA9,
      0x87,
      0x65,
      0x43,
      0x21,
      0x0F,
      0xED,
      0x1E,
      0x2F,
      0x30,
      0x41,
      0x52,
      0x63,
      0x74,
      0x85,
      0x96,
      0xA7,
      0xB8,
      0xC9,
      0xDA,
      0xEB,
      0xFC,
      0x0D,
    ]),
    '1000000F': Uint8List.fromList([
      0x0F,
      0x01,
      0x23,
      0x45,
      0x67,
      0x89,
      0xAB,
      0xCD,
      0xDC,
      0xBA,
      0x98,
      0x76,
      0x54,
      0x32,
      0x10,
      0xFE,
      0x1F,
      0x20,
      0x31,
      0x42,
      0x53,
      0x64,
      0x75,
      0x86,
      0x97,
      0xA8,
      0xB9,
      0xCA,
      0xDB,
      0xEC,
      0xFD,
      0x0E,
    ]),
    '10000010': Uint8List.fromList([
      0x10,
      0x02,
      0x34,
      0x56,
      0x78,
      0x9A,
      0xBC,
      0xDE,
      0xED,
      0xCB,
      0xA9,
      0x87,
      0x65,
      0x43,
      0x21,
      0x0F,
      0x20,
      0x21,
      0x32,
      0x43,
      0x54,
      0x65,
      0x76,
      0x87,
      0x98,
      0xA9,
      0xBA,
      0xCB,
      0xDC,
      0xED,
      0xFE,
      0x0F,
    ]),
  };

  // 密钥生成常量
  static const String _baseSalt = "aiTalk_group_salt_2025";
  static const String _kdfSalt = "aiTalk_group_key_derivation_v1";
  static const int _iterations = 10000;
  static const int _keyLength = 32; // 256位

  /// 检查群组是否启用了加密
  static Future<bool> isEncryptionEnabled(String groupId) async {
    try {
      // 公共群组始终启用加密（使用固定密钥）
      if (GroupUtil.isPublicGroup(groupId)) {
        return true;
      }

      // 私有群组检查数据库中是否存在密钥
      final db = await DatabaseService.instance.database;
      final result = await db.query(
        'group_encryption_keys',
        columns: ['group_id'],
        where: 'group_id = ?',
        whereArgs: [groupId],
        limit: 1,
      );
      return result.isNotEmpty;
    } catch (e) {
      debugPrint('❌ [GroupEncryptionKeyService] 检查加密状态失败: $e');
      return false;
    }
  }

  /// 获取群组的加密密钥
  static Future<Uint8List?> getEncryptionKey(String groupId) async {
    try {
      // 公共群组使用固定密钥
      if (GroupUtil.isPublicGroup(groupId)) {
        return _publicGroupKeys[groupId];
      }

      // 私有群组从数据库获取密钥
      final db = await DatabaseService.instance.database;
      final result = await db.query(
        'group_encryption_keys',
        columns: ['encryption_key'],
        where: 'group_id = ?',
        whereArgs: [groupId],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return result.first['encryption_key'] as Uint8List;
      }
      return null;
    } catch (e) {
      debugPrint('❌ [GroupEncryptionKeyService] 获取加密密钥失败: $e');
      return null;
    }
  }

  /// 为私有群组生成加密密钥
  static Future<bool> generatePrivateGroupKey(
    String groupId,
    int groupPassword,
    int createdAt,
  ) async {
    try {
      // 只为私有群组生成密钥
      if (GroupUtil.isPublicGroup(groupId)) {
        debugPrint('⚠️ [GroupEncryptionKeyService] 公共群组不需要生成密钥: $groupId');
        return false;
      }

      // 生成密钥
      final key = _generateKey(groupId, groupPassword);
      final salt = _generateSalt(groupId);

      // 保存到数据库
      final db = await DatabaseService.instance.database;
      await db.insert('group_encryption_keys', {
        'group_id': groupId,
        'encryption_key': key,
        'salt': salt,
        'created_at': createdAt,
        'updated_at': createdAt,
      }, conflictAlgorithm: ConflictAlgorithm.replace);

      debugPrint('✅ [GroupEncryptionKeyService] 私有群组密钥生成成功: $groupId');
      return true;
    } catch (e) {
      debugPrint('❌ [GroupEncryptionKeyService] 生成私有群组密钥失败: $e');
      return false;
    }
  }

  /// 删除群组的加密密钥
  static Future<bool> deleteEncryptionKey(String groupId) async {
    try {
      // 公共群组不能删除密钥
      if (GroupUtil.isPublicGroup(groupId)) {
        debugPrint('⚠️ [GroupEncryptionKeyService] 公共群组密钥不能删除: $groupId');
        return false;
      }

      final db = await DatabaseService.instance.database;
      final count = await db.delete(
        'group_encryption_keys',
        where: 'group_id = ?',
        whereArgs: [groupId],
      );

      debugPrint(
        '✅ [GroupEncryptionKeyService] 删除群组密钥: $groupId (影响行数: $count)',
      );
      return count > 0;
    } catch (e) {
      debugPrint('❌ [GroupEncryptionKeyService] 删除群组密钥失败: $e');
      return false;
    }
  }

  /// 生成群组加密密钥
  static Uint8List _generateKey(String groupId, int groupPassword) {
    // 1. 生成确定性盐值
    final salt = _generateSalt(groupId);

    // 2. 准备密钥材料
    final keyMaterial = _prepareKeyMaterial(groupId, groupPassword, salt);

    // 3. 使用PBKDF2派生密钥
    return _deriveKey(keyMaterial);
  }

  /// 生成基于群组ID的确定性盐值
  static String _generateSalt(String groupId) {
    final baseString = "${_baseSalt}_$groupId";
    return sha256.convert(utf8.encode(baseString)).toString().substring(0, 16);
  }

  /// 准备密钥材料
  static Uint8List _prepareKeyMaterial(
    String groupId,
    int groupPassword,
    String salt,
  ) {
    // 群组ID转字节（4字节）
    final groupIdInt = int.parse(groupId, radix: 16);
    final groupIdBytes = Uint8List(4);
    groupIdBytes.buffer.asByteData().setUint32(0, groupIdInt, Endian.little);

    // 群组密码转字节（4字节）
    final passwordBytes = Uint8List(4);
    passwordBytes.buffer.asByteData().setUint32(
      0,
      groupPassword,
      Endian.little,
    );

    // 盐值转字节
    final saltBytes = utf8.encode(salt);

    // 组合所有材料
    final keyMaterial = Uint8List(
      groupIdBytes.length + passwordBytes.length + saltBytes.length,
    );
    keyMaterial.setRange(0, groupIdBytes.length, groupIdBytes);
    keyMaterial.setRange(
      groupIdBytes.length,
      groupIdBytes.length + passwordBytes.length,
      passwordBytes,
    );
    keyMaterial.setRange(
      groupIdBytes.length + passwordBytes.length,
      keyMaterial.length,
      saltBytes,
    );

    return keyMaterial;
  }

  /// 使用PBKDF2派生密钥
  static Uint8List _deriveKey(Uint8List keyMaterial) {
    final pbkdf2 = PBKDF2KeyDerivator(HMac(SHA256Digest(), 64));
    pbkdf2.init(
      Pbkdf2Parameters(utf8.encode(_kdfSalt), _iterations, _keyLength),
    );

    return pbkdf2.process(keyMaterial);
  }
}
