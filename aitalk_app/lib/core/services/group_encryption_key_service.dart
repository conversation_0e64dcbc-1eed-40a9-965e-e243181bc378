import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:pointycastle/export.dart';
import 'package:sqflite/sqflite.dart';

import '../utils/group_util.dart';
import 'database_service.dart';

/// 群组加密密钥管理服务
/// 负责公共群固定密钥和私有群动态密钥的生成、存储和管理
class GroupEncryptionKeyService {
  GroupEncryptionKeyService._();

  static const String _appSalt = 'aiTalk_v1_encryption_salt_2024';

  /// 获取群组的加密密钥
  /// [groupId] 群组ID
  /// 返回32字节的加密密钥，如果群组不存在或未生成密钥则返回null
  static Future<Uint8List?> getGroupKey(String groupId) async {
    try {
      final db = await DatabaseService.instance.database;
      
      final rows = await db.query(
        'group_encryption_keys',
        columns: ['encryption_key'],
        where: 'group_id = ?',
        whereArgs: [groupId],
        limit: 1,
      );

      if (rows.isEmpty) {
        debugPrint('[GroupEncryptionKeyService] 群组 $groupId 的密钥不存在');
        return null;
      }

      final keyBase64 = rows.first['encryption_key'] as String;
      return base64Decode(keyBase64);
    } catch (e) {
      debugPrint('[GroupEncryptionKeyService] 获取群组密钥失败: $e');
      return null;
    }
  }

  /// 为私有群生成密钥
  /// [groupId] 群组ID
  /// [groupPassword] 群组密码
  /// [groupCreatedAt] 群组创建时间戳（毫秒）
  static Future<bool> generatePrivateGroupKey(
    String groupId,
    int groupPassword,
    int groupCreatedAt,
  ) async {
    try {
      final db = await DatabaseService.instance.database;

      // 检查密钥是否已存在
      final existing = await db.query(
        'group_encryption_keys',
        where: 'group_id = ?',
        whereArgs: [groupId],
        limit: 1,
      );

      if (existing.isNotEmpty) {
        debugPrint('[GroupEncryptionKeyService] 群组 $groupId 的密钥已存在');
        return true;
      }

      // 生成私有群密钥
      final key = _generatePrivateGroupMasterKey(
        groupId: groupId,
        groupPassword: groupPassword,
        groupCreatedAt: groupCreatedAt,
      );

      // 存储密钥
      final keyBase64 = base64Encode(key);
      await db.insert('group_encryption_keys', {
        'group_id': groupId,
        'encryption_key': keyBase64,
        'key_version': 1,
        'created_at': DateTime.now().millisecondsSinceEpoch,
      });

      debugPrint('[GroupEncryptionKeyService] 已为私有群 $groupId 生成加密密钥');
      return true;
    } catch (e) {
      debugPrint('[GroupEncryptionKeyService] 生成私有群密钥失败: $e');
      return false;
    }
  }

  /// 检查群组是否启用加密
  /// [groupId] 群组ID
  static Future<bool> isEncryptionEnabled(String groupId) async {
    try {
      final db = await DatabaseService.instance.database;
      
      final rows = await db.query(
        'group_encryption_keys',
        columns: ['group_id'],
        where: 'group_id = ?',
        whereArgs: [groupId],
        limit: 1,
      );

      return rows.isNotEmpty;
    } catch (e) {
      debugPrint('[GroupEncryptionKeyService] 检查加密状态失败: $e');
      return false;
    }
  }

  /// 生成私有群主密钥
  /// 确保所有群组成员生成相同的密钥
  static Uint8List _generatePrivateGroupMasterKey({
    required String groupId,
    required int groupPassword,
    required int groupCreatedAt,
  }) {
    // 1. 构建密码字符串：群组密码 + 群组ID + 固定盐值
    final passwordString = '$groupPassword$groupId$_appSalt';
    final passwordBytes = utf8.encode(passwordString);
    
    // 2. 构建盐值：群组创建时间 + 群组ID
    final saltString = '$groupCreatedAt$groupId';
    final saltBytes = utf8.encode(saltString);
    
    // 3. 使用PBKDF2生成32字节主密钥
    final pbkdf2 = PBKDF2KeyDerivator(HMac(SHA256Digest(), 64));
    pbkdf2.init(Pbkdf2Parameters(saltBytes, 100000, 32));
    
    return pbkdf2.process(passwordBytes);
  }

  /// 为消息生成数据加密密钥
  /// [masterKey] 群组主密钥
  /// [timestamp] 消息时间戳
  static Uint8List deriveDataKey(Uint8List masterKey, int timestamp) {
    final hkdf = HKDFKeyDerivator(SHA256Digest());
    final salt = Uint8List.fromList(timestamp.toString().codeUnits);
    final info = utf8.encode('aitalk-data-encryption-v1');
    
    hkdf.init(HkdfParameters(masterKey, 32, salt, info));
    return hkdf.process(Uint8List(0));
  }

  /// 删除群组密钥（群组解散时调用）
  /// [groupId] 群组ID
  static Future<bool> deleteGroupKey(String groupId) async {
    try {
      final db = await DatabaseService.instance.database;
      
      final count = await db.delete(
        'group_encryption_keys',
        where: 'group_id = ?',
        whereArgs: [groupId],
      );

      debugPrint('[GroupEncryptionKeyService] 已删除群组 $groupId 的密钥 (影响行数: $count)');
      return count > 0;
    } catch (e) {
      debugPrint('[GroupEncryptionKeyService] 删除群组密钥失败: $e');
      return false;
    }
  }

  /// 获取所有已加密的群组列表
  static Future<List<String>> getEncryptedGroups() async {
    try {
      final db = await DatabaseService.instance.database;
      
      final rows = await db.query(
        'group_encryption_keys',
        columns: ['group_id'],
      );

      return rows.map((row) => row['group_id'] as String).toList();
    } catch (e) {
      debugPrint('[GroupEncryptionKeyService] 获取加密群组列表失败: $e');
      return [];
    }
  }
}
