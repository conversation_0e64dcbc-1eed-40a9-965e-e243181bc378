import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:sqflite/sqflite.dart';
import '../models/user_qr_data.dart';
import '../protocol/tk8620_request_sender.dart';
import '../protocol/at_commands.dart';
import '../bluetooth/passthrough_gatt_helper.dart';
import '../bluetooth/bluetooth_manager.dart';
import '../protocol/work_mode_constants.dart';
import '../constants/frequencies.dart';
import 'active_group_storage.dart';
import 'conversation_manager.dart';
import 'database_service.dart';

/// 联系人服务
/// 负责管理联系人的添加、更新和查询
class ContactService {
  ContactService._();

  static final ContactService instance = ContactService._();

  // 联系人功能暂不实现，只保留群组功能

  /// 加入群组
  /// [groupData] 从二维码解码得到的群组数据
  /// [currentUserDeviceId] 当前用户的设备ID
  /// 返回是否加入成功
  Future<bool> joinGroup(
    GroupQrData groupData,
    String currentUserDeviceId,
  ) async {
    try {
      final db = await DatabaseService.instance.database;
      final int nowMs = DateTime.now().millisecondsSinceEpoch;
      final int nowSec = nowMs ~/ 1000;

      // 检查群组是否已存在
      final existingGroup = await db.query(
        'groups',
        where: 'group_id = ?',
        whereArgs: [groupData.groupId],
        limit: 1,
      );

      if (existingGroup.isEmpty) {
        // 群组不存在，添加群组信息
        await db.insert('groups', {
          'group_id': groupData.groupId,
          'group_name': groupData.groupName,
          'channel': groupData.channel,
          'is_private': 1, // 从二维码加入的都是私有群
          'password': groupData.password,
          'creator_id': groupData.members[0] ?? 'unknown', // 群主是member_id为0的成员
          'created_at': nowMs,
          'updated_at': nowMs,
        }, conflictAlgorithm: ConflictAlgorithm.replace);
        debugPrint('✅ 添加群组: ${groupData.groupName} (${groupData.groupId})');
      }

      // 添加群组成员到contacts表
      for (final entry in groupData.members.entries) {
        final memberId = entry.key;
        final deviceId = entry.value;

        // 确保成员在contacts表中存在
        await db.insert('contacts', {
          'device_id': deviceId,
          'nickname': '群成员 - $memberId',
          'avatar_index': 0,
          'created_at': nowMs,
          'updated_at': nowMs,
        }, conflictAlgorithm: ConflictAlgorithm.ignore);

        // 添加到群组成员表
        await db.insert('group_members', {
          'group_id': groupData.groupId,
          'device_id': deviceId,
          'member_id': memberId,
          'joined_at': nowMs,
        }, conflictAlgorithm: ConflictAlgorithm.ignore);
      }

      // 检查当前用户是否已在群组中
      final currentUserInGroup = await db.query(
        'group_members',
        where: 'group_id = ? AND device_id = ?',
        whereArgs: [groupData.groupId, currentUserDeviceId],
        limit: 1,
      );

      int currentMemberId;

      if (currentUserInGroup.isEmpty) {
        // 当前用户不在群组中，添加当前用户到群组
        // 分配一个新的member_id
        final maxMemberIdResult = await db.rawQuery(
          'SELECT MAX(member_id) as max_id FROM group_members WHERE group_id = ?',
          [groupData.groupId],
        );
        final maxMemberId = maxMemberIdResult.first['max_id'] as int? ?? -1;
        currentMemberId = maxMemberId + 1;

        await db.insert('group_members', {
          'group_id': groupData.groupId,
          'device_id': currentUserDeviceId,
          'member_id': currentMemberId,
          'joined_at': nowMs,
        }, conflictAlgorithm: ConflictAlgorithm.replace);
        debugPrint(
          '✅ 当前用户加入群组: ${groupData.groupName} (member_id: $currentMemberId)',
        );
      } else {
        // 当前用户已在群组中，获取现有的member_id
        currentMemberId = currentUserInGroup.first['member_id'] as int;
        debugPrint(
          '✅ 当前用户已在群组中: ${groupData.groupName} (member_id: $currentMemberId)',
        );
      }

      // 无论是否已在群组中，都发送加入会话通知（重新激活/同步状态）
      await _sendJoinNotification(
        memberId: currentMemberId,
        deviceId: int.parse(
          currentUserDeviceId.replaceFirst('0x', ''),
          radix: 16,
        ),
        sessionId: int.parse(
          groupData.groupId.replaceFirst('group_', ''),
          radix: 16,
        ),
        groupData: groupData,
      );

      // 创建或更新群组会话
      await db.insert('group_conversations', {
        'conversation_id': groupData.groupId,
        'group_id': groupData.groupId,
        'unread_count': 0,
        'last_msg_time': nowSec,
      }, conflictAlgorithm: ConflictAlgorithm.ignore);

      // 通知群组变更
      DatabaseService.groupChangedNotifier.value++;

      debugPrint('✅ 成功加入群组: ${groupData.groupName}');
      return true;
    } catch (e) {
      debugPrint('❌ 加入群组失败: $e');
      return false;
    }
  }

  /// 发送加入会话通知给群组内其他成员
  Future<void> _sendJoinNotification({
    required int memberId,
    required int deviceId,
    required int sessionId,
    required GroupQrData groupData,
  }) async {
    try {
      // 获取当前连接的蓝牙设备
      final currentDevice = BluetoothManager.currentDevice.value;
      if (currentDevice == null) {
        debugPrint('❌ 无法发送加入会话通知: 没有连接的蓝牙设备');
        return;
      }

      debugPrint('🔧 配置设备参数准备发送加入会话通知...');

      // 1. 设置频率（从群组二维码的信道转换而来）
      // 使用私有群信道频率映射表
      final frequency = Frequencies.privateChannelFreq(groupData.channel);
      final freqBytes = getAtCommandBytes(
        AtCommandType.setFreq,
        params: {
          'txDataFreq': frequency,
          'rxDataFreq': frequency,
          'txBcnFreq': frequency,
          'rxBcnFreq': frequency,
        },
      );
      await PassthroughGattHelper.sendAtCommand(
        currentDevice,
        freqBytes,
        withoutResponse: true,
      );
      debugPrint('✅ 频率设置完成: ${frequency}Hz (私有信道${groupData.channel})');

      // 等待配置生效
      await Future.delayed(const Duration(milliseconds: 100));

      // 2. 发送加入会话通知
      await TK8620RequestSender.sendJoinNotification(
        currentDevice,
        memberId: memberId,
        deviceId: deviceId,
        sessionId: sessionId,
      );

      debugPrint('✅ 加入会话通知发送成功');

      // 3. 激活新加入的群组
      try {
        await _activateGroup(groupData, deviceId);
        debugPrint('✅ 群组激活完成: ${groupData.groupName}');
      } catch (e) {
        debugPrint('❌ 群组激活失败: $e');
      }
    } catch (e) {
      debugPrint('❌ 发送加入会话通知失败: $e');
    }
  }

  /// 激活新加入的群组
  Future<void> _activateGroup(GroupQrData groupData, int deviceId) async {
    try {
      final groupId = groupData.groupId.replaceFirst('group_', '');
      // 获取群主的设备ID（memberId为0的成员）
      final creatorDeviceIdStr =
          groupData.members[0] ??
          '0x${deviceId.toRadixString(16).padLeft(8, '0').toUpperCase()}';
      final creatorId = creatorDeviceIdStr;

      debugPrint('🎯 激活群组: $groupId (${groupData.groupName})');

      // 1. 激活会话管理器中的群组
      ConversationManager.enter(groupId);

      // 2. 保存激活群组到持久化存储
      await ActiveGroupStorage.save(groupId: groupId, creatorId: creatorId);

      // 3. 通知UI刷新
      DatabaseService.groupChangedNotifier.value++;

      // 4. 确保状态变更被正确传播
      await Future.delayed(const Duration(milliseconds: 100));
    } catch (e) {
      debugPrint('❌ 激活群组失败: $e');
    }
  }

  // 联系人查询功能暂不实现
}
