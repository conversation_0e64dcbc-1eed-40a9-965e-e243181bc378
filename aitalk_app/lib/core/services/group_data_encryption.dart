import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:pointycastle/export.dart';

import 'group_encryption_key_service.dart';

/// 群组数据加密/解密服务
/// 使用确定性密钥流异或加密，确保密文长度与明文长度完全相同
/// 不增加任何额外字节
class GroupDataEncryption {
  GroupDataEncryption._();

  // 加密参数
  static const int _keyStreamBlockSize = 16; // AES块大小

  /// 加密群组数据
  ///
  /// [groupId] 群组ID
  /// [plaintext] 要加密的明文数据
  ///
  /// 返回加密后的数据，格式：IV(16字节) + 密文（与明文长度相同）
  /// 如果加密失败返回null
  static Future<Uint8List?> encrypt(String groupId, Uint8List plaintext) async {
    try {
      // 获取群组加密密钥
      final key = await GroupEncryptionKeyService.getEncryptionKey(groupId);
      if (key == null) {
        debugPrint('❌ [GroupDataEncryption] 群组密钥不存在: $groupId');
        return null;
      }

      // 生成随机IV
      final iv = _generateRandomIV();

      // 执行AES-CTR加密
      final ciphertext = _performAesCtrEncryption(key, iv, plaintext);
      if (ciphertext == null) {
        debugPrint('❌ [GroupDataEncryption] AES-CTR加密失败');
        return null;
      }

      // 组合输出：IV + 密文（密文长度与明文相同）
      final output = Uint8List(_ivLength + ciphertext.length);
      output.setRange(0, _ivLength, iv);
      output.setRange(_ivLength, output.length, ciphertext);

      debugPrint(
        '✅ [GroupDataEncryption] 数据加密成功: ${plaintext.length}字节 -> ${output.length}字节 (密文${ciphertext.length}字节)',
      );
      return output;
    } catch (e) {
      debugPrint('❌ [GroupDataEncryption] 加密过程异常: $e');
      return null;
    }
  }

  /// 解密群组数据
  ///
  /// [groupId] 群组ID
  /// [ciphertext] 要解密的密文数据，格式：IV(16字节) + 密文
  ///
  /// 返回解密后的明文数据，如果解密失败返回null
  static Future<Uint8List?> decrypt(
    String groupId,
    Uint8List ciphertext,
  ) async {
    try {
      // 检查密文长度
      if (ciphertext.length < _ivLength) {
        debugPrint('❌ [GroupDataEncryption] 密文长度不足: ${ciphertext.length}字节');
        return null;
      }

      // 获取群组加密密钥
      final key = await GroupEncryptionKeyService.getEncryptionKey(groupId);
      if (key == null) {
        debugPrint('❌ [GroupDataEncryption] 群组密钥不存在: $groupId');
        return null;
      }

      // 提取IV
      final iv = ciphertext.sublist(0, _ivLength);

      // 提取密文
      final encryptedData = ciphertext.sublist(_ivLength);

      // 执行AES-CTR解密
      final plaintext = _performAesCtrDecryption(key, iv, encryptedData);
      if (plaintext == null) {
        debugPrint('❌ [GroupDataEncryption] AES-CTR解密失败');
        return null;
      }

      debugPrint(
        '✅ [GroupDataEncryption] 数据解密成功: ${ciphertext.length}字节 -> ${plaintext.length}字节',
      );
      return plaintext;
    } catch (e) {
      debugPrint('❌ [GroupDataEncryption] 解密过程异常: $e');
      return null;
    }
  }

  /// 检查数据是否已加密
  ///
  /// 通过检查数据长度来判断是否为加密数据
  /// 加密数据的最小长度为：IV(16) = 16字节
  static bool isEncrypted(Uint8List data) {
    return data.length > _ivLength;
  }

  /// 生成随机IV
  static Uint8List _generateRandomIV() {
    final random = Random.secure();
    final iv = Uint8List(_ivLength);
    for (int i = 0; i < _ivLength; i++) {
      iv[i] = random.nextInt(256);
    }
    return iv;
  }

  /// 执行AES-CTR加密
  ///
  /// [key] 256位加密密钥
  /// [iv] 128位初始化向量
  /// [plaintext] 明文数据
  ///
  /// 返回密文，长度与明文相同，失败返回null
  static Uint8List? _performAesCtrEncryption(
    Uint8List key,
    Uint8List iv,
    Uint8List plaintext,
  ) {
    try {
      // 创建AES-CTR加密器
      final cipher = CTRStreamCipher(AESEngine());
      final params = ParametersWithIV(KeyParameter(key), iv);

      // 初始化加密器
      cipher.init(true, params);

      // 执行加密
      final output = Uint8List(plaintext.length);
      cipher.processBytes(plaintext, 0, plaintext.length, output, 0);

      return output;
    } catch (e) {
      debugPrint('❌ [GroupDataEncryption] AES-CTR加密异常: $e');
      return null;
    }
  }

  /// 执行AES-CTR解密
  ///
  /// [key] 256位加密密钥
  /// [iv] 128位初始化向量
  /// [ciphertext] 密文数据
  ///
  /// 返回明文数据，失败返回null
  static Uint8List? _performAesCtrDecryption(
    Uint8List key,
    Uint8List iv,
    Uint8List ciphertext,
  ) {
    try {
      // 创建AES-CTR解密器（CTR模式加密和解密使用相同操作）
      final cipher = CTRStreamCipher(AESEngine());
      final params = ParametersWithIV(KeyParameter(key), iv);

      // 初始化解密器
      cipher.init(false, params);

      // 执行解密
      final output = Uint8List(ciphertext.length);
      cipher.processBytes(ciphertext, 0, ciphertext.length, output, 0);

      return output;
    } catch (e) {
      debugPrint('❌ [GroupDataEncryption] AES-CTR解密异常: $e');
      return null;
    }
  }

  /// 加密文本消息
  ///
  /// [groupId] 群组ID
  /// [message] 文本消息
  ///
  /// 返回加密后的字节数据，失败返回null
  static Future<Uint8List?> encryptTextMessage(
    String groupId,
    String message,
  ) async {
    try {
      final plaintext = Uint8List.fromList(message.codeUnits);
      return await encrypt(groupId, plaintext);
    } catch (e) {
      debugPrint('❌ [GroupDataEncryption] 文本消息加密失败: $e');
      return null;
    }
  }

  /// 解密文本消息
  ///
  /// [groupId] 群组ID
  /// [ciphertext] 加密的字节数据
  ///
  /// 返回解密后的文本消息，失败返回null
  static Future<String?> decryptTextMessage(
    String groupId,
    Uint8List ciphertext,
  ) async {
    try {
      final plaintext = await decrypt(groupId, ciphertext);
      if (plaintext == null) return null;

      return String.fromCharCodes(plaintext);
    } catch (e) {
      debugPrint('❌ [GroupDataEncryption] 文本消息解密失败: $e');
      return null;
    }
  }

  /// 加密语音数据
  ///
  /// [groupId] 群组ID
  /// [voiceData] 语音数据字节
  ///
  /// 返回加密后的字节数据，失败返回null
  static Future<Uint8List?> encryptVoiceData(
    String groupId,
    Uint8List voiceData,
  ) async {
    return await encrypt(groupId, voiceData);
  }

  /// 解密语音数据
  ///
  /// [groupId] 群组ID
  /// [ciphertext] 加密的语音数据
  ///
  /// 返回解密后的语音数据，失败返回null
  static Future<Uint8List?> decryptVoiceData(
    String groupId,
    Uint8List ciphertext,
  ) async {
    return await decrypt(groupId, ciphertext);
  }
}
