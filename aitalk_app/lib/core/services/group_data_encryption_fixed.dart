import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:pointycastle/export.dart';

import 'group_encryption_key_service.dart';

/// 群组数据加密/解密服务
/// 使用确定性密钥流异或加密，确保密文长度与明文长度完全相同
/// 不增加任何额外字节
class GroupDataEncryption {
  GroupDataEncryption._();

  /// 加密群组数据
  /// 
  /// [groupId] 群组ID
  /// [plaintext] 要加密的明文数据
  /// 
  /// 返回加密后的数据，长度与明文完全相同
  /// 如果加密失败返回null
  static Future<Uint8List?> encrypt(String groupId, Uint8List plaintext) async {
    try {
      // 获取群组加密密钥
      final key = await GroupEncryptionKeyService.getEncryptionKey(groupId);
      if (key == null) {
        debugPrint('❌ [GroupDataEncryption] 群组密钥不存在: $groupId');
        return null;
      }

      // 生成确定性密钥流并异或加密
      final ciphertext = _performStreamCipherEncryption(key, plaintext, groupId);
      if (ciphertext == null) {
        debugPrint('❌ [GroupDataEncryption] 流密码加密失败');
        return null;
      }

      debugPrint('✅ [GroupDataEncryption] 数据加密成功: ${plaintext.length}字节 -> ${ciphertext.length}字节 (长度不变)');
      return ciphertext;
    } catch (e) {
      debugPrint('❌ [GroupDataEncryption] 加密过程异常: $e');
      return null;
    }
  }

  /// 解密群组数据
  /// 
  /// [groupId] 群组ID
  /// [ciphertext] 要解密的密文数据
  /// 
  /// 返回解密后的明文数据，长度与密文完全相同
  /// 如果解密失败返回null
  static Future<Uint8List?> decrypt(String groupId, Uint8List ciphertext) async {
    try {
      // 获取群组加密密钥
      final key = await GroupEncryptionKeyService.getEncryptionKey(groupId);
      if (key == null) {
        debugPrint('❌ [GroupDataEncryption] 群组密钥不存在: $groupId');
        return null;
      }

      // 生成相同的确定性密钥流并异或解密（流密码加密和解密是相同操作）
      final plaintext = _performStreamCipherEncryption(key, ciphertext, groupId);
      if (plaintext == null) {
        debugPrint('❌ [GroupDataEncryption] 流密码解密失败');
        return null;
      }

      debugPrint('✅ [GroupDataEncryption] 数据解密成功: ${ciphertext.length}字节 -> ${plaintext.length}字节 (长度不变)');
      return plaintext;
    } catch (e) {
      debugPrint('❌ [GroupDataEncryption] 解密过程异常: $e');
      return null;
    }
  }

  /// 检查数据是否已加密
  /// 
  /// 由于加密后长度不变，无法通过长度判断，总是返回false
  /// 需要通过其他方式（如协议标志）来判断数据是否加密
  static bool isEncrypted(Uint8List data) {
    // 长度不变的加密无法通过数据本身判断是否加密
    return false;
  }

  /// 执行流密码加密/解密
  /// 
  /// [key] 256位加密密钥
  /// [data] 要处理的数据
  /// [groupId] 群组ID（用于生成确定性IV）
  /// 
  /// 返回处理后的数据，长度与输入相同，失败返回null
  static Uint8List? _performStreamCipherEncryption(
    Uint8List key,
    Uint8List data,
    String groupId,
  ) {
    try {
      if (data.isEmpty) return Uint8List(0);

      // 使用群组ID生成确定性IV（确保相同群组相同位置的数据使用相同密钥流）
      final iv = _generateDeterministicIV(groupId);

      // 生成足够长度的密钥流
      final keyStream = _generateKeyStream(key, iv, data.length);

      // 异或加密/解密
      final result = Uint8List(data.length);
      for (int i = 0; i < data.length; i++) {
        result[i] = data[i] ^ keyStream[i];
      }

      return result;
    } catch (e) {
      debugPrint('❌ [GroupDataEncryption] 流密码处理异常: $e');
      return null;
    }
  }

  /// 生成确定性IV
  /// 
  /// 基于群组ID生成固定的IV，确保相同群组使用相同的密钥流
  static Uint8List _generateDeterministicIV(String groupId) {
    // 使用群组ID的哈希作为IV
    final groupIdBytes = Uint8List.fromList(groupId.codeUnits);
    final digest = SHA256Digest();
    final hash = digest.process(groupIdBytes);
    
    // 取前16字节作为IV
    return hash.sublist(0, 16);
  }

  /// 生成密钥流
  /// 
  /// 使用AES-CTR模式生成指定长度的密钥流
  static Uint8List _generateKeyStream(Uint8List key, Uint8List iv, int length) {
    final cipher = CTRStreamCipher(AESEngine());
    final params = ParametersWithIV(KeyParameter(key), iv);
    
    cipher.init(true, params);
    
    // 生成全零数据并加密得到密钥流
    final zeros = Uint8List(length);
    final keyStream = Uint8List(length);
    cipher.processBytes(zeros, 0, length, keyStream, 0);
    
    return keyStream;
  }

  /// 加密文本消息
  /// 
  /// [groupId] 群组ID
  /// [message] 文本消息
  /// 
  /// 返回加密后的字节数据，长度与原始UTF-8字节相同，失败返回null
  static Future<Uint8List?> encryptTextMessage(String groupId, String message) async {
    try {
      final plaintext = Uint8List.fromList(message.codeUnits);
      return await encrypt(groupId, plaintext);
    } catch (e) {
      debugPrint('❌ [GroupDataEncryption] 文本消息加密失败: $e');
      return null;
    }
  }

  /// 解密文本消息
  /// 
  /// [groupId] 群组ID
  /// [ciphertext] 加密的字节数据
  /// 
  /// 返回解密后的文本消息，失败返回null
  static Future<String?> decryptTextMessage(String groupId, Uint8List ciphertext) async {
    try {
      final plaintext = await decrypt(groupId, ciphertext);
      if (plaintext == null) return null;
      
      return String.fromCharCodes(plaintext);
    } catch (e) {
      debugPrint('❌ [GroupDataEncryption] 文本消息解密失败: $e');
      return null;
    }
  }

  /// 加密语音数据
  /// 
  /// [groupId] 群组ID
  /// [voiceData] 语音数据字节
  /// 
  /// 返回加密后的字节数据，长度与原始数据相同，失败返回null
  static Future<Uint8List?> encryptVoiceData(String groupId, Uint8List voiceData) async {
    return await encrypt(groupId, voiceData);
  }

  /// 解密语音数据
  /// 
  /// [groupId] 群组ID
  /// [ciphertext] 加密的语音数据
  /// 
  /// 返回解密后的语音数据，长度与密文相同，失败返回null
  static Future<Uint8List?> decryptVoiceData(String groupId, Uint8List ciphertext) async {
    return await decrypt(groupId, ciphertext);
  }
}
