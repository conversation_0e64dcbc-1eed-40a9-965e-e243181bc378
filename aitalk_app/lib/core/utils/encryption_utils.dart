import 'package:flutter/foundation.dart';

import '../services/group_data_encryption.dart';
import '../utils/group_util.dart';

/// 加密工具类
/// 提供便捷的加密/解密接口，用于协议层集成
class EncryptionUtils {
  EncryptionUtils._();

  /// 检查群组是否需要加密
  ///
  /// [groupId] 群组ID
  ///
  /// 返回true表示需要加密，false表示不需要加密
  static bool shouldEncrypt(String groupId) {
    // 所有群组（公共群和私有群）都需要加密
    return groupId.isNotEmpty;
  }

  /// 加密消息payload
  ///
  /// [groupId] 群组ID
  /// [payload] 原始payload数据
  ///
  /// 返回加密后的payload，如果不需要加密或加密失败则返回原始数据
  static Future<Uint8List> encryptPayload(
    String groupId,
    Uint8List payload,
  ) async {
    try {
      // 检查是否需要加密
      if (!shouldEncrypt(groupId)) {
        debugPrint('🔓 [EncryptionUtils] 群组无需加密: $groupId');
        return payload;
      }

      // 执行加密
      final encrypted = await GroupDataEncryption.encrypt(groupId, payload);
      if (encrypted != null) {
        debugPrint(
          '🔒 [EncryptionUtils] Payload加密成功: ${payload.length}字节 -> ${encrypted.length}字节',
        );
        return encrypted;
      } else {
        debugPrint('❌ [EncryptionUtils] Payload加密失败，返回原始数据');
        return payload;
      }
    } catch (e) {
      debugPrint('❌ [EncryptionUtils] Payload加密异常: $e，返回原始数据');
      return payload;
    }
  }

  /// 解密消息payload
  ///
  /// [groupId] 群组ID
  /// [encryptedPayload] 加密的payload数据
  ///
  /// 返回解密后的payload，如果不需要解密或解密失败则返回原始数据
  static Future<Uint8List> decryptPayload(
    String groupId,
    Uint8List encryptedPayload,
  ) async {
    try {
      // 检查是否需要解密
      if (!shouldEncrypt(groupId)) {
        debugPrint('🔓 [EncryptionUtils] 群组无需解密: $groupId');
        return encryptedPayload;
      }

      // 检查数据是否为加密格式
      if (!GroupDataEncryption.isEncrypted(encryptedPayload)) {
        debugPrint('🔓 [EncryptionUtils] 数据未加密，直接返回');
        return encryptedPayload;
      }

      // 执行解密
      final decrypted = await GroupDataEncryption.decrypt(
        groupId,
        encryptedPayload,
      );
      if (decrypted != null) {
        debugPrint(
          '🔓 [EncryptionUtils] Payload解密成功: ${encryptedPayload.length}字节 -> ${decrypted.length}字节',
        );
        return decrypted;
      } else {
        debugPrint('❌ [EncryptionUtils] Payload解密失败，返回原始数据');
        return encryptedPayload;
      }
    } catch (e) {
      debugPrint('❌ [EncryptionUtils] Payload解密异常: $e，返回原始数据');
      return encryptedPayload;
    }
  }

  /// 加密文本消息
  ///
  /// [groupId] 群组ID
  /// [message] 文本消息
  ///
  /// 返回加密后的字节数据，失败返回原始消息的字节数据
  static Future<Uint8List> encryptTextMessage(
    String groupId,
    String message,
  ) async {
    try {
      if (!shouldEncrypt(groupId)) {
        return Uint8List.fromList(message.codeUnits);
      }

      final encrypted = await GroupDataEncryption.encryptTextMessage(
        groupId,
        message,
      );
      if (encrypted != null) {
        debugPrint('🔒 [EncryptionUtils] 文本消息加密成功');
        return encrypted;
      } else {
        debugPrint('❌ [EncryptionUtils] 文本消息加密失败，返回原始数据');
        return Uint8List.fromList(message.codeUnits);
      }
    } catch (e) {
      debugPrint('❌ [EncryptionUtils] 文本消息加密异常: $e');
      return Uint8List.fromList(message.codeUnits);
    }
  }

  /// 解密文本消息
  ///
  /// [groupId] 群组ID
  /// [encryptedData] 加密的字节数据
  ///
  /// 返回解密后的文本消息，失败返回原始数据转换的字符串
  static Future<String> decryptTextMessage(
    String groupId,
    Uint8List encryptedData,
  ) async {
    try {
      if (!shouldEncrypt(groupId)) {
        return String.fromCharCodes(encryptedData);
      }

      // 检查数据是否为加密格式
      if (!GroupDataEncryption.isEncrypted(encryptedData)) {
        return String.fromCharCodes(encryptedData);
      }

      final decrypted = await GroupDataEncryption.decryptTextMessage(
        groupId,
        encryptedData,
      );
      if (decrypted != null) {
        debugPrint('🔓 [EncryptionUtils] 文本消息解密成功');
        return decrypted;
      } else {
        debugPrint('❌ [EncryptionUtils] 文本消息解密失败，返回原始数据');
        return String.fromCharCodes(encryptedData);
      }
    } catch (e) {
      debugPrint('❌ [EncryptionUtils] 文本消息解密异常: $e');
      return String.fromCharCodes(encryptedData);
    }
  }

  /// 加密语音数据
  ///
  /// [groupId] 群组ID
  /// [voiceData] 语音数据字节
  ///
  /// 返回加密后的字节数据，失败返回原始数据
  static Future<Uint8List> encryptVoiceData(
    String groupId,
    Uint8List voiceData,
  ) async {
    try {
      if (!shouldEncrypt(groupId)) {
        return voiceData;
      }

      final encrypted = await GroupDataEncryption.encryptVoiceData(
        groupId,
        voiceData,
      );
      if (encrypted != null) {
        debugPrint('🔒 [EncryptionUtils] 语音数据加密成功');
        return encrypted;
      } else {
        debugPrint('❌ [EncryptionUtils] 语音数据加密失败，返回原始数据');
        return voiceData;
      }
    } catch (e) {
      debugPrint('❌ [EncryptionUtils] 语音数据加密异常: $e');
      return voiceData;
    }
  }

  /// 解密语音数据
  ///
  /// [groupId] 群组ID
  /// [encryptedData] 加密的语音数据
  ///
  /// 返回解密后的语音数据，失败返回原始数据
  static Future<Uint8List> decryptVoiceData(
    String groupId,
    Uint8List encryptedData,
  ) async {
    try {
      if (!shouldEncrypt(groupId)) {
        return encryptedData;
      }

      // 检查数据是否为加密格式
      if (!GroupDataEncryption.isEncrypted(encryptedData)) {
        return encryptedData;
      }

      final decrypted = await GroupDataEncryption.decryptVoiceData(
        groupId,
        encryptedData,
      );
      if (decrypted != null) {
        debugPrint('🔓 [EncryptionUtils] 语音数据解密成功');
        return decrypted;
      } else {
        debugPrint('❌ [EncryptionUtils] 语音数据解密失败，返回原始数据');
        return encryptedData;
      }
    } catch (e) {
      debugPrint('❌ [EncryptionUtils] 语音数据解密异常: $e');
      return encryptedData;
    }
  }

  /// 获取群组类型描述（用于日志）
  static String getGroupTypeDescription(String groupId) {
    if (GroupUtil.isPublicGroup(groupId)) {
      final channel = GroupUtil.channelFromGroupId(groupId);
      return '公共群(信道$channel)';
    } else {
      return '私有群';
    }
  }
}
