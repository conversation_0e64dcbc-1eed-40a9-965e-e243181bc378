/// 语音数据多包组装器
/// 在收到最后一包 (paddingByte bit7 = 1) 时，返回合并后的 TK8620VoiceData。
/// 非尾包返回 null，调用方可自行解码实时播放。

import 'dart:typed_data';
import 'tk8620_frame_decoder.dart';
import 'tk8620_protocol.dart';
import 'package:flutter/foundation.dart';

class _VoiceAssembly {
  final List<Uint8List> chunks = [];

  void add(Uint8List data) => chunks.add(data);

  Uint8List merge() {
    final builder = BytesBuilder();
    for (final c in chunks) {
      builder.add(c);
    }
    return builder.toBytes();
  }
}

/// 提供 `handleVoiceFrame`，仅在收齐完整语音时返回结果。
class VoicePacketAssembler {
  // key 使用 srcId，若后续需要支持并发语音，可扩展为 srcId+index。
  final Map<int, _VoiceAssembly> _cache = {};

  Future<TK8620VoiceData?> handleVoiceFrame(TK8620Frame frame) async {
    // 从目标ID获取群组ID用于解密
    final groupId = frame.dstId.toRadixString(16).padLeft(8, '0').toUpperCase();

    final vd = await TK8620PayloadParser.parseVoiceData(
      frame.payload,
      groupId: groupId,
    );
    if (vd == null) return null;

    final key = frame.srcId;
    _cache.putIfAbsent(key, () => _VoiceAssembly());
    _cache[key]!.add(vd.audioData);

    if (vd.isLastPacket) {
      debugPrint('🔚 收到语音数据最后一包 (${vd.audioData.length}B)');
      final merged = _cache[key]!.merge();
      _cache.remove(key);
      return TK8620VoiceData(audioData: merged, isLastPacket: true);
    }

    return null; // 仍未完结
  }
}
