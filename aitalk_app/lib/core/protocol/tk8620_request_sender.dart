// TK8620 射频通信协议请求发送器
// 参考规范: aiTalk SDD §4.1.3.1 & §4.1.3.2
// 该文件实现了TK8620协议帧的构建和发送功能，与 TK8620ResponseParser 相对应

import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

import 'tk8620_protocol.dart';
import 'tk8620_frame_encoder.dart';
import '../bluetooth/passthrough_gatt_helper.dart';
import '../device/device_manager.dart';
import '../user/user_profile.dart';
import '../utils/encryption_utils.dart';
import '../services/conversation_manager.dart';
import 'at_commands.dart';
import '../constants/rate_mode_payload.dart';

/// 临时存储用户输入的群组密码，用于在收到入群响应时保存到数据库
class JoinPasswordCache {
  static int? _lastJoinPassword;

  static void setPassword(int password) {
    _lastJoinPassword = password;
  }

  static int? getAndClearPassword() {
    final password = _lastJoinPassword;
    _lastJoinPassword = null;
    return password;
  }
}

/// TK8620协议请求发送器
/// 负责构建和发送TK8620协议帧，类似于DeviceControlRequestSender
class TK8620RequestSender {
  TK8620RequestSender._();

  /// 构建一个完整的TK8620协议帧，返回待发送字节序列
  static Uint8List buildFrame({
    required TK8620FrameType frameType,
    required TK8620CommunicationMode communicationMode,
    int version = TK8620Version.aiTalk_1_0,
    int frameCnt = 0,
    required int srcId,
    int dstId = 0,
    int subPkgNum = 1,
    int subPkgNo = 0,
    required Uint8List payload,
    bool useLongSrcId = false,
  }) {
    return TK8620FrameEncoder.buildFrame(
      frameType: frameType,
      communicationMode: communicationMode,
      version: version,
      frameCnt: frameCnt,
      srcId: srcId,
      dstId: dstId,
      subPkgNum: subPkgNum,
      subPkgNo: subPkgNo,
      payload: payload,
      useLongSrcId: useLongSrcId,
    );
  }

  /// 直接发送TK8620协议帧到指定设备
  static Future<void> send(
    BluetoothDevice device, {
    required TK8620FrameType frameType,
    required TK8620CommunicationMode communicationMode,
    int version = TK8620Version.aiTalk_1_0,
    int frameCnt = 0,
    required int srcId,
    int dstId = 0,
    int subPkgNum = 1,
    int subPkgNo = 0,
    required Uint8List payload,
    bool withoutResponse = true,
    bool useLongSrcId = false,
  }) async {
    final frame = buildFrame(
      frameType: frameType,
      communicationMode: communicationMode,
      version: version,
      frameCnt: frameCnt,
      srcId: srcId,
      dstId: dstId,
      subPkgNum: subPkgNum,
      subPkgNo: subPkgNo,
      payload: payload,
      useLongSrcId: useLongSrcId,
    );

    // 打印协议帧信息用于调试
    debugPrint('发送TK8620协议帧:');
    debugPrint('  帧类型: ${frameType.name}');
    debugPrint('  通信模式: ${communicationMode.name}');
    debugPrint('  源ID: $srcId');
    debugPrint('  目标ID: $dstId');
    debugPrint('  载荷长度: ${payload.length}');
    debugPrint(
      '  完整帧: ${frame.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}',
    );

    // 使用PassthroughGattHelper发送TK8620协议帧
    await PassthroughGattHelper.sendFrame(
      device,
      frame,
      withoutResponse: withoutResponse,
    );
  }

  // ---------------- 便捷方法 ----------------

  /// 发送入群请求
  static Future<void> sendJoinGroupRequest(
    BluetoothDevice device, {
    required int channel,
    required String password,
    String? userName,
    int? srcId,
  }) async {
    // 将密码字符串转换为数字 (简单实现，实际可能需要更复杂的哈希)
    int sessionPassword = 0;
    if (password.isNotEmpty) {
      try {
        sessionPassword = int.parse(password);
      } catch (e) {
        // 如果不是纯数字，使用字符串哈希
        sessionPassword = password.hashCode & 0xFFFFFFFF;
      }
    }

    // 缓存密码，用于在收到入群响应时保存到数据库
    JoinPasswordCache.setPassword(sessionPassword);

    // 若未提供 srcId, 尝试从 DeviceManager 获取
    int resolvedSrcId = srcId ?? 0x01;
    final deviceIdHex = DeviceManager.instance.deviceIdNotifier.value;
    if (srcId == null && deviceIdHex != null && deviceIdHex.startsWith('0x')) {
      try {
        resolvedSrcId =
            int.parse(deviceIdHex.substring(2), radix: 16) & 0xFFFFFFFF;
      } catch (_) {}
    }

    // 若未提供 userName, 尝试从 UserProfile 获取
    final resolvedUserName =
        userName ?? UserProfile.instance.nicknameNotifier.value ?? 'User';

    // 构建会话请求载荷
    final payload = TK8620PayloadBuilder.buildJoinRequest(
      sessionPassword: sessionPassword,
      userName: resolvedUserName,
    );

    // 封装完整协议帧 (4字节SrcID, 广播DstID)
    final frame = TK8620RequestSender.buildFrame(
      frameType: TK8620FrameType.session,
      communicationMode: TK8620CommunicationMode.data,
      srcId: resolvedSrcId,
      dstId: 0xFFFFFFFF,
      payload: payload,
      useLongSrcId: true,
    );

    // 通过 AT+SENDB 指令发送 (内部含转义)
    final atBytes = getAtCommandBytes(
      AtCommandType.sendBinary,
      params: {'binaryData': frame},
    );

    await PassthroughGattHelper.sendAtCommand(
      device,
      atBytes,
      withoutResponse: true,
    );

    // 调试信息
    debugPrint(
      '入群请求已通过 AT+SENDB 发送: 信道:$channel, srcId:$resolvedSrcId, user:$resolvedUserName',
    );
  }

  /// 发送入群响应
  static Future<void> sendJoinGroupResponse(
    BluetoothDevice device, {
    required int responseCode,
    required int memberId,
    required int sessionId,
    required String groupName,
    List<int> memberDeviceIds = const [],
    required int srcId,
    int dstId = 0,
  }) async {
    // 构建会话响应载荷
    final payload = TK8620PayloadBuilder.buildJoinResponse(
      responseCode: responseCode,
      memberId: memberId,
      sessionId: sessionId,
      groupName: groupName,
      memberDeviceIds: memberDeviceIds,
    );

    // ⚠️ 加入会话响应同样使用 4 字节 SrcID，且通过 AT+SENDB 发送
    final frame = TK8620RequestSender.buildFrame(
      frameType: TK8620FrameType.session,
      communicationMode: TK8620CommunicationMode.data,
      srcId: srcId,
      dstId: dstId,
      payload: payload,
      useLongSrcId: true,
    );

    // 封装成 AT 指令（自动加 escape & CRLF）
    final atBytes = getAtCommandBytes(
      AtCommandType.sendBinary,
      params: {'binaryData': frame},
    );

    await PassthroughGattHelper.sendAtCommand(
      device,
      atBytes,
      withoutResponse: true,
    );

    // 调试输出
    debugPrint(
      '入群响应已通过 AT+SENDB 发送: code:$responseCode, sessionId:0x${sessionId.toRadixString(16).padLeft(8, '0')}, srcId:$srcId, dstId:$dstId',
    );
  }

  /// 发送建立通话请求
  static Future<void> sendCreateTalkRequest(
    BluetoothDevice device, {
    required int sessionId,
    required int srcId,
    int dstId = 0,
  }) async {
    // 构建建立通话请求载荷
    final payload = TK8620PayloadBuilder.buildCreateTalkRequest(
      sessionId: sessionId,
    );

    // 封装完整协议帧（使用4字节SrcID，与其他session帧保持一致）
    final frame = TK8620RequestSender.buildFrame(
      frameType: TK8620FrameType.session,
      communicationMode: TK8620CommunicationMode.realTime,
      srcId: srcId,
      dstId: dstId,
      payload: payload,
      useLongSrcId: true, // 实时通话帧使用4字节SrcID
    );

    // 通过 AT+SENDB 指令发送
    final atBytes = getAtCommandBytes(
      AtCommandType.sendBinary,
      params: {'binaryData': frame},
    );

    await PassthroughGattHelper.sendAtCommand(
      device,
      atBytes,
      withoutResponse: true,
    );

    debugPrint(
      '✅ 发送建立通话请求完成 -> sessionId: 0x${sessionId.toRadixString(16).padLeft(8, '0')}',
    );
  }

  /// 发送建立通话响应
  static Future<void> sendCreateTalkResponse(
    BluetoothDevice device, {
    required int responseCode,
    required int sessionId,
    required int srcId,
    int dstId = 0,
  }) async {
    // 构建建立通话响应载荷
    final payload = TK8620PayloadBuilder.buildCreateTalkResponse(
      responseCode: responseCode,
      sessionId: sessionId,
    );

    // 封装完整协议帧（使用4字节SrcID，与其他session帧保持一致）
    final frame = TK8620RequestSender.buildFrame(
      frameType: TK8620FrameType.session,
      communicationMode: TK8620CommunicationMode.realTime,
      srcId: srcId,
      dstId: dstId,
      payload: payload,
      useLongSrcId: true, // 实时通话帧使用4字节SrcID
    );

    // 通过 AT+SENDB 指令发送
    final atBytes = getAtCommandBytes(
      AtCommandType.sendBinary,
      params: {'binaryData': frame},
    );

    await PassthroughGattHelper.sendAtCommand(
      device,
      atBytes,
      withoutResponse: true,
    );

    debugPrint(
      '✅ 发送建立通话响应完成 -> code: $responseCode, sessionId: 0x${sessionId.toRadixString(16).padLeft(8, '0')}',
    );
  }

  /// 发送加入会话通知
  static Future<void> sendJoinNotification(
    BluetoothDevice device, {
    required int memberId,
    required int deviceId,
    required int sessionId,
  }) async {
    // 构建加入会话通知载荷
    final payload = TK8620PayloadBuilder.buildJoinNotification(
      memberId: memberId,
    );

    // 封装完整协议帧
    // SrcID: 新加入成员的DeviceID (4字节)
    // DstID: 群组ID (SessionID)
    final frame = TK8620RequestSender.buildFrame(
      frameType: TK8620FrameType.session,
      communicationMode: TK8620CommunicationMode.data,
      srcId: deviceId,
      dstId: sessionId,
      payload: payload,
      useLongSrcId: true, // 会话帧使用4字节SrcID
    );

    // 通过 AT+SENDB 指令发送
    final atBytes = getAtCommandBytes(
      AtCommandType.sendBinary,
      params: {'binaryData': frame},
    );

    await PassthroughGattHelper.sendAtCommand(
      device,
      atBytes,
      withoutResponse: true,
    );

    debugPrint(
      '✅ 发送加入会话通知完成 -> memberId: $memberId, deviceId: 0x${deviceId.toRadixString(16).padLeft(8, '0')}, sessionId: 0x${sessionId.toRadixString(16).padLeft(8, '0')}',
    );
  }

  /// 发送文本消息
  static Future<void> sendTextMessage(
    BluetoothDevice device, {
    required String text,
    required int srcId,
    int dstId = 0,
    int frameCnt = 0,
  }) async {
    // 获取当前群组ID用于加密
    final groupId = dstId.toRadixString(16).padLeft(8, '0').toUpperCase();

    // 加密文本消息
    final encryptedTextBytes = await EncryptionUtils.encryptTextMessage(
      groupId,
      text,
    );

    // 使用加密后的数据
    final textBytes = encryptedTextBytes;

    // 计算头部长度（空载荷时）
    final emptyFrameForLen = TK8620RequestSender.buildFrame(
      frameType: TK8620FrameType.data,
      communicationMode: TK8620CommunicationMode.data,
      frameCnt: frameCnt,
      srcId: srcId,
      dstId: dstId,
      payload: Uint8List(0),
    );
    final headerLen = emptyFrameForLen.length;

    final maxFrameLen = RateModePayload.currentMaxPayload;
    final maxPayloadPerFrame = maxFrameLen - headerLen;

    // 每个分包需要预留1字节给数据类型码
    final maxTextBytesPerFrame = maxPayloadPerFrame > 1
        ? maxPayloadPerFrame - 1
        : maxPayloadPerFrame;

    // 根据文本字节长度计算分包数
    final totalChunks =
        (textBytes.length + maxTextBytesPerFrame - 1) ~/ maxTextBytesPerFrame;

    for (int i = 0; i < totalChunks; i++) {
      final start = i * maxTextBytesPerFrame;
      final end = (start + maxTextBytesPerFrame) > textBytes.length
          ? textBytes.length
          : start + maxTextBytesPerFrame;
      final textChunk = textBytes.sublist(start, end);

      // 为每个分包构建完整的载荷（数据类型码 + 文本数据）
      final buffer = BytesBuilder();
      buffer.addByte(TK8620DataType.text); // 每个分包都需要数据类型码
      buffer.add(textChunk);
      final payload = buffer.toBytes();

      final frame = TK8620RequestSender.buildFrame(
        frameType: TK8620FrameType.data,
        communicationMode: TK8620CommunicationMode.data,
        frameCnt: frameCnt,
        srcId: srcId,
        dstId: dstId,
        subPkgNum: totalChunks,
        subPkgNo: i,
        payload: payload,
      );

      final atBytes = getAtCommandBytes(
        AtCommandType.sendBinary,
        params: {'binaryData': frame},
      );

      await PassthroughGattHelper.sendAtCommand(
        device,
        atBytes,
        withoutResponse: true,
      );

      debugPrint(
        '文字消息包[$i/$totalChunks] 已发送 (textBytes ${textChunk.length}B, payload ${payload.length}B, frame ${frame.length}B)',
      );

      // 若不是最后一包, 根据速率模式等待一定间隔
      if (i < totalChunks - 1) {
        await Future.delayed(
          Duration(milliseconds: RateModePayload.currentInterFrameDelay),
        );
      }
    }

    debugPrint(
      '文字消息已通过 AT+SENDB 分包发送: 共 $totalChunks 包, srcId:$srcId -> dstId:$dstId, 文本:"$text"',
    );
  }

  /// 发送GPS位置信息
  static Future<void> sendGPSLocation(
    BluetoothDevice device, {
    required double latitude,
    required double longitude,
    required double altitude,
    required int srcId,
    int dstId = 0,
    int frameCnt = 0,
  }) async {
    final payload = TK8620PayloadBuilder.buildGPSData(
      latitude: latitude,
      longitude: longitude,
      altitude: altitude,
    );

    await send(
      device,
      frameType: TK8620FrameType.data,
      communicationMode: TK8620CommunicationMode.data,
      frameCnt: frameCnt,
      srcId: srcId,
      dstId: dstId,
      payload: payload,
    );
  }

  /// 发送语音数据（PTT）
  static Future<void> sendVoiceData(
    BluetoothDevice device, {
    required Uint8List audioData,
    required int srcId,
    bool isLastPacket = false,
  }) async {
    // 获取当前群组ID用于加密
    final groupId = ConversationManager.currentConversationId.value;

    // 加密语音数据（如果有群组ID）
    Uint8List processedAudioData = audioData;
    if (groupId != null && groupId.isNotEmpty && audioData.isNotEmpty) {
      processedAudioData = await EncryptionUtils.encryptVoiceData(
        groupId,
        audioData,
      );
    }

    // 如果没有任何音频数据，需要发送一个仅含 paddingByte 的结束包
    if (audioData.isEmpty && isLastPacket) {
      final payload = TK8620PayloadBuilder.buildVoiceData(
        audioData: Uint8List(0),
        isLastPacket: true,
      );

      final frame = TK8620RequestSender.buildFrame(
        frameType: TK8620FrameType.voice,
        communicationMode: TK8620CommunicationMode.data,
        srcId: srcId,
        subPkgNo: 0,
        payload: payload,
      );

      final atBytes = getAtCommandBytes(
        AtCommandType.sendBinary,
        params: {'binaryData': frame},
      );

      await PassthroughGattHelper.sendAtCommand(
        device,
        atBytes,
        withoutResponse: true,
      );

      debugPrint('语音数据包[0/1] 已发送 (audio 0B, frame ${frame.length}B)');

      return;
    }

    // 计算头部长度（空载荷时）
    final emptyFrameForLen = TK8620RequestSender.buildFrame(
      frameType: TK8620FrameType.voice,
      communicationMode: TK8620CommunicationMode.data,
      srcId: srcId,
      subPkgNo: 0,
      payload: Uint8List(0),
    );
    final headerLen = emptyFrameForLen.length;

    // 最大有效载荷长度（不含协议头）
    final maxFrameLen = RateModePayload.currentMaxPayload;
    final maxPayloadPerFrame = maxFrameLen - headerLen;

    // 语音载荷需预留1字节paddingByte
    final maxAudioPerFrame = maxPayloadPerFrame > 1
        ? maxPayloadPerFrame - 1
        : maxPayloadPerFrame;

    // 计算总包数（使用加密后的数据）
    final totalChunks =
        (processedAudioData.length + maxAudioPerFrame - 1) ~/ maxAudioPerFrame;

    for (int i = 0; i < totalChunks; i++) {
      final start = i * maxAudioPerFrame;
      final end = (start + maxAudioPerFrame) > processedAudioData.length
          ? processedAudioData.length
          : start + maxAudioPerFrame;
      final chunk = processedAudioData.sublist(start, end);

      // 构建本包语音载荷
      final payload = TK8620PayloadBuilder.buildVoiceData(
        audioData: Uint8List.fromList(chunk),
        isLastPacket: isLastPacket && (i == totalChunks - 1),
      );

      // 构建协议帧
      final frame = TK8620RequestSender.buildFrame(
        frameType: TK8620FrameType.voice,
        communicationMode: TK8620CommunicationMode.data,
        srcId: srcId,
        subPkgNo: i,
        payload: payload,
      );

      // 封装 AT 指令并发送
      final atBytes = getAtCommandBytes(
        AtCommandType.sendBinary,
        params: {'binaryData': frame},
      );

      await PassthroughGattHelper.sendAtCommand(
        device,
        atBytes,
        withoutResponse: true,
      );

      debugPrint(
        '语音数据包[$i/$totalChunks] 已发送 (audio ${chunk.length}B, frame ${frame.length}B)',
      );

      // 若不是最后一包，按速率模式等待一定间隔
      if (i < totalChunks - 1) {
        await Future.delayed(
          Duration(milliseconds: RateModePayload.currentInterFrameDelay),
        );
      }
    }
  }

  /// 发送会话建立通知
  static Future<void> sendSessionEstablish(
    BluetoothDevice device, {
    required int sessionId,
    required String sessionName,
    required int srcId,
    int dstId = 0,
  }) async {
    final payload = TK8620PayloadBuilder.buildSessionEstablish(
      sessionId: sessionId,
      sessionName: sessionName,
    );

    await send(
      device,
      frameType: TK8620FrameType.session,
      communicationMode: TK8620CommunicationMode.data,
      srcId: srcId,
      dstId: dstId,
      payload: payload,
    );
  }

  /// 发送会话终止通知
  static Future<void> sendSessionTerminate(
    BluetoothDevice device, {
    required int sessionId,
    required int reasonCode,
    required int srcId,
    int dstId = 0,
  }) async {
    final payload = TK8620PayloadBuilder.buildSessionTerminate(
      sessionId: sessionId,
      reasonCode: reasonCode,
    );

    await send(
      device,
      frameType: TK8620FrameType.session,
      communicationMode: TK8620CommunicationMode.data,
      srcId: srcId,
      dstId: dstId,
      payload: payload,
    );
  }

  /// 发送实时通话语音数据
  static Future<void> sendRealTimeVoiceData(
    BluetoothDevice device, {
    required Uint8List audioData,
    required int srcId,
    bool isLastPacket = false,
  }) async {
    // 获取当前群组ID用于加密
    final groupId = ConversationManager.currentConversationId.value;

    // 加密语音数据（如果有群组ID）
    Uint8List processedAudioData = audioData;
    if (groupId != null && groupId.isNotEmpty && audioData.isNotEmpty) {
      processedAudioData = await EncryptionUtils.encryptVoiceData(
        groupId,
        audioData,
      );
    }

    // 构建语音数据载荷（与PTT相同的格式，不需要数据类型字节）
    final payload = TK8620PayloadBuilder.buildVoiceData(
      audioData: processedAudioData,
      isLastPacket: isLastPacket,
    );

    // 使用语音帧类型，简化协议头部（与PTT相同）
    final frame = TK8620RequestSender.buildFrame(
      frameType: TK8620FrameType.voice,
      communicationMode: TK8620CommunicationMode.realTime,
      srcId: srcId & 0xFF, // 只使用1字节SrcID
      subPkgNo: 0,
      payload: payload,
      useLongSrcId: false, // 实时通话使用1字节SrcID，与PTT保持一致
    );

    // 通过 AT+SENDB 指令发送
    final atBytes = getAtCommandBytes(
      AtCommandType.sendBinary,
      params: {'binaryData': frame},
    );

    await PassthroughGattHelper.sendAtCommand(
      device,
      atBytes,
      withoutResponse: true,
    );
  }
}
