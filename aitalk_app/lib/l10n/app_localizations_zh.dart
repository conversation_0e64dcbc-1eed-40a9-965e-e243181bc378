// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get autoReconnect_cancel => '取消';

  @override
  String get autoReconnect_message => '正在尝试重新连接...';

  @override
  String get bottomNav_call => '通话';

  @override
  String get bottomNav_chat => '消息';

  @override
  String get bottomNav_contacts => '联系人';

  @override
  String get bottomNav_profile => '我的';

  @override
  String get callList_searchHint => '搜索';

  @override
  String get callList_title => '通话记录';

  @override
  String get callList_noMembersError => '群组成员不足，无法发起通话';

  @override
  String get callList_loadMembersError => '获取群组成员失败';

  @override
  String get callList_selectCallType => '选择通话方式';

  @override
  String get callList_selectCallTypeDesc => '该群组有2个成员，请选择通话方式：';

  @override
  String get callList_pttTalk => 'PTT对讲';

  @override
  String get callList_voiceCall => '实时通话';

  @override
  String get callList_voiceCallNotImplemented => '实时通话功能开发中...';

  @override
  String get callList_noRecords => '暂无通话记录';

  @override
  String get callList_connected => '已接通';

  @override
  String get callList_missed => '未接听';

  @override
  String get callList_outgoing => '已拨出';

  @override
  String get callList_yesterday => '昨天';

  @override
  String get voiceCall_calling => '通话中...';

  @override
  String get voiceCall_ended => '通话已结束';

  @override
  String get voiceCall_demoMessage => '实时语音通话功能正在开发中，这是一个演示界面。';

  @override
  String get channelPicker_cancel => '取消';

  @override
  String channelPicker_channel(Object channel) {
    return '信道 - $channel';
  }

  @override
  String get channelPicker_title => '选择信道';

  @override
  String get chatList_connectDevice => '连接设备';

  @override
  String chatList_connectionFailed(Object error) {
    return '设备连接失败: $error';
  }

  @override
  String chatList_connectionSuccess(Object device) {
    return '设备连接成功: $device';
  }

  @override
  String get chatList_createGroup => '创建群组';

  @override
  String get chatList_disconnectDevice => '断开连接';

  @override
  String chatList_disconnectFailed(Object error) {
    return '断开连接失败: $error';
  }

  @override
  String get chatList_disconnectSuccess => '设备已断开连接';

  @override
  String get chatList_joinGroup => '加入群组';

  @override
  String get chatList_searchHint => '搜索';

  @override
  String chatList_unpairFailed(Object error) {
    return '解除绑定失败: $error';
  }

  @override
  String get chatList_unpairSuccess => '已解除设备绑定';

  @override
  String get createGroup_cancel => '取消';

  @override
  String get createGroup_done => '完成';

  @override
  String get createGroup_selectChannel => '选择信道';

  @override
  String get createGroup_setGroupName => '设置群名称';

  @override
  String get createGroup_setPassword => '设置群密码';

  @override
  String get createGroup_title => '创建群组';

  @override
  String get deviceInfo_activateTurMass => '激活 TurMass™';

  @override
  String get deviceInfo_bleVersion => '蓝牙固件版本';

  @override
  String get deviceInfo_deviceBattery => '设备电量';

  @override
  String get deviceInfo_deviceId => '设备 ID';

  @override
  String get deviceInfo_deviceId_failed => '获取失败';

  @override
  String get deviceInfo_deviceId_loading => '获取中…';

  @override
  String get deviceInfo_deviceInfo => '设备信息';

  @override
  String get deviceInfo_deviceModel => '设备型号';

  @override
  String get deviceInfo_deviceName => '设备名称';

  @override
  String get deviceInfo_deviceOperation => '设备操作';

  @override
  String get deviceInfo_deviceVersion => '设备版本';

  @override
  String get deviceInfo_disconnect => '断开设备';

  @override
  String get deviceInfo_hwVersion => '硬件版本';

  @override
  String get deviceInfo_title => '设备信息';

  @override
  String get deviceInfo_turMassFirmwareVersion => 'TurMass™ 固件版本';

  @override
  String get deviceInfo_unpair => '解除设备';

  @override
  String get global_appTitle => 'aiTalk';

  @override
  String get global_cancel => '取消';

  @override
  String get joinGroup_title => '加入群组';

  @override
  String get notification_showContent => '显示消息详情';

  @override
  String get notification_showContent_desc => '在通知中显示消息预览';

  @override
  String get notification_system => '系统消息通知';

  @override
  String get notification_system_desc => '接收系统推送的通知';

  @override
  String get notification_voiceCall => '语音通话通知';

  @override
  String get notification_voiceCall_desc => '当有语音通话时提醒';

  @override
  String get profile_avatar => '头像';

  @override
  String get profile_developerMode => '开发者模式';

  @override
  String profile_deviceId(Object id) {
    return '设备ID: $id';
  }

  @override
  String get profile_deviceManagement => '设备管理';

  @override
  String get profile_language => '语言';

  @override
  String get profile_languageChinese => '中文';

  @override
  String get profile_languageEnglish => '英文';

  @override
  String get profile_languageSystem => '跟随系统';

  @override
  String get profile_myInfo => '我的资料';

  @override
  String get profile_nickname => '昵称';

  @override
  String get profile_nickname_default => '默认用户';

  @override
  String get profile_qrcode => '二维码';

  @override
  String get profile_qrcode_title => '我的二维码';

  @override
  String get profile_qrcode_scanHint => '扫描二维码添加好友';

  @override
  String get profile_qrcode_myInfo => '我的信息';

  @override
  String get profile_qrcode_nickname => '昵称';

  @override
  String get profile_qrcode_deviceId => '设备ID';

  @override
  String get profile_qrcode_channelType => '信道类型';

  @override
  String get profile_qrcode_channelNumber => '信道号';

  @override
  String get profile_qrcode_frequency => '频点';

  @override
  String get profile_qrcode_rateMode => '速率模式';

  @override
  String get profile_qrcode_publicChannel => '公共信道';

  @override
  String get profile_qrcode_privateChannel => '私有信道';

  @override
  String get profile_qrcode_copyData => '复制二维码数据';

  @override
  String get profile_qrcode_dataCopied => '二维码数据已复制到剪贴板';

  @override
  String get profile_qrcode_generateFailed => '生成二维码失败';

  @override
  String get profile_qrcode_retry => '重试';

  @override
  String get profile_qrcode_noData => '无法生成二维码';

  @override
  String get profile_qrcode_saveImage => '保存二维码';

  @override
  String get profile_qrcode_imageSaved => '二维码已保存到相册';

  @override
  String get profile_qrcode_saveFailed => '保存失败';

  @override
  String get profile_settings => '设置';

  @override
  String get profile_signature => '个性签名';

  @override
  String get profile_userGuide => '用户指南';

  @override
  String get publicChat_inputHint => '输入文字消息…';

  @override
  String get publicChat_pressHold => '按住说话';

  @override
  String get publicChat_recording => '正在录音...';

  @override
  String get publicGroup_chatHistory => '聊天记录';

  @override
  String get publicGroup_chatInfo => '聊天信息';

  @override
  String get publicGroup_deleteHistory => '删除记录';

  @override
  String get publicGroup_detail_title => '公共群详情';

  @override
  String get publicGroup_editChannel => '编辑信道';

  @override
  String get publicGroup_groupName => '群名称';

  @override
  String publicGroup_name(Object channel) {
    return '公共群 - $channel';
  }

  @override
  String get publicGroup_searchHistory => '搜索记录';

  @override
  String get privateGroup_cancel => '取消';

  @override
  String get privateGroup_changePassword => '更改密码';

  @override
  String get privateGroup_chatHistory => '聊天记录';

  @override
  String get privateGroup_chatInfo => '聊天信息';

  @override
  String get privateGroup_deleteHistory => '删除记录';

  @override
  String get privateGroup_detail_title => '群组详情';

  @override
  String get privateGroup_done => '完成';

  @override
  String get privateGroup_editChannel => '编辑信道';

  @override
  String get privateGroup_groupChannel => '群组信道';

  @override
  String get privateGroup_groupMembers => '群成员';

  @override
  String get privateGroup_groupName => '群名称';

  @override
  String get privateGroup_groupPassword => '群组密码';

  @override
  String get privateGroup_groupQRCode => '群二维码';

  @override
  String get groupQR_title => '群组二维码';

  @override
  String get groupQR_scanHint => '扫描二维码加入群组';

  @override
  String get groupQR_groupInfo => '群组信息';

  @override
  String get groupQR_groupName => '群组名称';

  @override
  String get groupQR_groupId => '群组ID';

  @override
  String get groupQR_channel => '信道';

  @override
  String get groupQR_password => '密码';

  @override
  String get groupQR_memberCount => '成员数量';

  @override
  String get groupQR_noPassword => '无密码';

  @override
  String get groupQR_saveSuccess => '群组二维码已保存到相册';

  @override
  String get groupQR_saveFailed => '保存失败';

  @override
  String get groupQR_generateFailed => '生成群组二维码失败';

  @override
  String get groupQR_members => '成员';

  @override
  String get groupQR_owner => '群主';

  @override
  String get groupQR_saveTooltip => '保存二维码';

  @override
  String get groupQR_needPermission => '需要相册访问权限才能保存图片';

  @override
  String get privateGroup_leaveGroup => '解散群组';

  @override
  String get privateGroup_searchHistory => '搜索记录';

  @override
  String get settings_about => '关于 aiTalk';

  @override
  String get settings_chatSettings => '聊天设置';

  @override
  String get settings_displaySettings => '显示设置';

  @override
  String get settings_fontSize => '文字大小';

  @override
  String get settings_helpAndFeedback => '帮助和反馈';

  @override
  String get settings_multilanguage => '多语言';

  @override
  String get settings_notifications => '消息通知';

  @override
  String get settings_other => '其他';

  @override
  String get settings_storageManagement => '存储管理';

  @override
  String get settings_storageSettings => '存储设置';

  @override
  String get settings_themeDark => '暗黑';

  @override
  String get settings_themeLight => '明亮';

  @override
  String get settings_themeMode => '主题模式';

  @override
  String get settings_themeSystem => '跟随系统';

  @override
  String get settings_title => '设置';

  @override
  String settings_version(Object version) {
    return 'Version $version';
  }

  @override
  String get settings_voicePlayback => '语音播放';

  @override
  String get voicePlayback_autoPlay => '自动播放语音';

  @override
  String get voicePlayback_autoPlay_desc => '收到语音消息时自动播放';

  @override
  String get voicePlayback_backgroundMode => '后台播放模式';

  @override
  String get voicePlayback_backgroundMode_desc => '应用切换到后台时继续播放语音消息';

  @override
  String get chatAction_ptt => 'PTT对讲';

  @override
  String get chatAction_voiceCall => '实时通话';

  @override
  String get voiceCall_waitingAnswer => '等待接听...';

  @override
  String get voiceCall_hangUp => '挂断';

  @override
  String get voiceCall_speaker => '扬声器';

  @override
  String get voiceCall_earpiece => '听筒';

  @override
  String get voiceCall_incomingCall => '来电';

  @override
  String get voiceCall_realTimeCall => '实时通话';

  @override
  String get voiceCall_accept => '接听';

  @override
  String get voiceCall_reject => '拒绝';

  @override
  String get ptt_noMembers => '暂无成员';

  @override
  String get storage_totalTitle => 'aiTalk 数据';

  @override
  String get storage_cache => '缓存';

  @override
  String get storage_cacheDesc => '缓存用于临时存储应用运行过程中生成的数据，例如图片、文件预览和临时消息。清理缓存可释放空间，不会影响聊天记录或个性化设置。';

  @override
  String get storage_cacheClear => '清理';

  @override
  String get storage_cacheCleared => '已清理缓存';

  @override
  String get storage_chathistory => '聊天记录';

  @override
  String get storage_chathistoryDesc => '聊天记录包含所有与其他用户的会话，包括文本、图片和文件。清除后将永久删除所有聊天内容，无法恢复，请谨慎操作。';

  @override
  String get storage_chathistoryManage => '管理';

  @override
  String get storage_appdata => 'App 数据';

  @override
  String get storage_appdataDesc => 'App 数据是指应用自身占用的存储空间，包括必要的运行文件和语音算法模型参数。这些数据是应用正常运行所必需的，无法删除。';

  @override
  String get settings_textSize => '文字大小';

  @override
  String textSize_demoMessage(Object name) {
    return '我是$name';
  }

  @override
  String get chatList_voicePreview => '[语音]';

  @override
  String get chatList_locationPreview => '[位置]';

  @override
  String get chatList_messagePreview => '[消息]';

  @override
  String get addContact_title => '添加联系人';

  @override
  String get addContact_scanGroupQR => '扫描群组二维码';

  @override
  String get addContact_scanContactQR => '扫描联系人二维码';

  @override
  String get addContact_scanGroupQRDesc => '扫描群组二维码加入群聊';

  @override
  String get addContact_scanContactQRDesc => '扫描联系人二维码添加好友';

  @override
  String get qrScanner_title => '扫描二维码';

  @override
  String get qrScanner_scanHint => '将二维码放入框内进行扫描';

  @override
  String get qrScanner_permissionDenied => '相机权限被拒绝';

  @override
  String get qrScanner_permissionRequired => '需要相机权限才能扫描二维码';

  @override
  String get qrScanner_scanFailed => '扫描失败';

  @override
  String get qrScanner_invalidQRCode => '无效的二维码';
}
