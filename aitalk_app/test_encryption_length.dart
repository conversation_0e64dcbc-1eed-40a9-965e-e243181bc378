// 简单的加密长度测试
import 'dart:typed_data';
import 'lib/core/services/group_data_encryption.dart';
import 'lib/core/services/group_encryption_key_service.dart';

void main() async {
  print('=== 加密长度测试 ===');
  
  // 测试数据
  const testGroupId = 'ABC12345';
  const testPassword = 123456;
  const testMessage = 'hi';
  final testVoiceData = Uint8List.fromList([0x01, 0x02, 0x03, 0x04, 0x05]);
  
  try {
    // 生成密钥
    print('1. 生成测试密钥...');
    final keyGenerated = await GroupEncryptionKeyService.generatePrivateGroupKey(
      testGroupId,
      testPassword,
      DateTime.now().millisecondsSinceEpoch,
    );
    
    if (!keyGenerated) {
      print('❌ 密钥生成失败');
      return;
    }
    print('✅ 密钥生成成功');
    
    // 测试文本加密长度
    print('\n2. 测试文本消息加密长度');
    print('原始文本: "$testMessage"');
    print('原始长度: ${testMessage.codeUnits.length}字节');
    
    final encryptedText = await GroupDataEncryption.encryptTextMessage(testGroupId, testMessage);
    if (encryptedText != null) {
      print('加密后长度: ${encryptedText.length}字节');
      print('长度增加: ${encryptedText.length - testMessage.codeUnits.length}字节');
      print('组成: IV(16字节) + 密文(${encryptedText.length - 16}字节)');
      
      // 验证解密
      final decrypted = await GroupDataEncryption.decryptTextMessage(testGroupId, encryptedText);
      print('解密结果: ${decrypted == testMessage ? "✅ 正确" : "❌ 错误"}');
    }
    
    // 测试语音数据加密长度
    print('\n3. 测试语音数据加密长度');
    print('原始语音数据长度: ${testVoiceData.length}字节');
    
    final encryptedVoice = await GroupDataEncryption.encryptVoiceData(testGroupId, testVoiceData);
    if (encryptedVoice != null) {
      print('加密后长度: ${encryptedVoice.length}字节');
      print('长度增加: ${encryptedVoice.length - testVoiceData.length}字节');
      print('组成: IV(16字节) + 密文(${encryptedVoice.length - 16}字节)');
      
      // 验证解密
      final decryptedVoice = await GroupDataEncryption.decryptVoiceData(testGroupId, encryptedVoice);
      if (decryptedVoice != null) {
        bool isEqual = true;
        if (decryptedVoice.length == testVoiceData.length) {
          for (int i = 0; i < testVoiceData.length; i++) {
            if (decryptedVoice[i] != testVoiceData[i]) {
              isEqual = false;
              break;
            }
          }
        } else {
          isEqual = false;
        }
        print('解密结果: ${isEqual ? "✅ 正确" : "❌ 错误"}');
      }
    }
    
    print('\n=== 测试完成 ===');
    print('✅ 新的AES-CTR加密确保密文长度 = 明文长度 + 16字节(IV)');
    print('✅ 相比之前的AES-GCM，大大减少了长度开销');
    
  } catch (e) {
    print('❌ 测试过程中发生异常: $e');
  }
}
