# 群组数据加密功能实现文档

## 功能概述

为aiTalk应用实现了完整的群组数据加密功能，包含以下特性：

1. **16个公共群组使用固定密钥**
2. **私有群组使用动态生成的密钥**
3. **AES-256-GCM加密算法**
4. **只加密业务数据payload，协议帧头不加密**
5. **基于群组ID和群密码的确定性密钥生成**

## 实现的文件

### 1. 密钥管理服务 (`lib/core/services/group_encryption_key_service.dart`)
- `GroupEncryptionKeyService` 类提供密钥管理功能
- 16个公共群组的固定密钥定义
- 私有群组密钥的生成、存储和获取
- 基于PBKDF2-HMAC-SHA256的密钥派生算法

### 2. 数据加密服务 (`lib/core/services/group_data_encryption.dart`)
- `GroupDataEncryption` 类提供数据加密/解密功能
- 使用AES-256-GCM算法
- 支持文本消息和语音数据的加密
- 自动处理IV生成和认证标签

### 3. 加密工具类 (`lib/core/utils/encryption_utils.dart`)
- `EncryptionUtils` 类提供便捷的加密接口
- 适用于协议层集成
- 自动处理加密失败的降级策略
- 支持各种数据类型的加密

### 4. 数据库表结构
- 新增 `group_encryption_keys` 表存储私有群组密钥
- 数据库版本升级到v4
- 支持密钥的创建、查询和删除

## 加密算法详细设计

### 密钥生成算法

**输入参数：**
- `groupId`: 群组ID（4字节16进制字符串）
- `groupPassword`: 群组密码（int类型）
- `salt`: 确定性盐值（基于群组ID生成）

**密钥生成流程：**
```
1. 生成确定性盐值：sha256("aiTalk_group_salt_2025_" + groupId).substring(0, 16)
2. 准备密钥材料：groupId(4字节) + groupPassword(4字节) + salt
3. 使用PBKDF2-HMAC-SHA256派生密钥：
   - 输入：密钥材料
   - 盐值："aiTalk_group_key_derivation_v1"
   - 迭代次数：10000
   - 输出长度：32字节（256位）
```

### 加密算法

**算法：** AES-256-CTR（流密码模式）
- **密钥长度：** 256位（32字节）
- **IV长度：** 128位（16字节）
- **特点：** 密文长度与明文长度完全相同

**加密输出格式：**
```
[IV(16字节)] + [密文（与明文长度相同）]
```

**优势：**
- 密文长度 = 明文长度 + 16字节（仅IV开销）
- 不会改变协议数据包的大小结构
- 流密码模式，适合任意长度数据加密

### 公共群组密钥

16个公共群组（信道1-16）使用预定义的固定密钥：
- 群组ID: `10000001` - `10000010`（对应信道1-16）
- 每个群组有独立的32字节密钥
- 密钥在代码中硬编码，所有设备使用相同密钥

## 数据库结构

```sql
CREATE TABLE group_encryption_keys (
  group_id        TEXT PRIMARY KEY,       -- 群组ID
  encryption_key  BLOB NOT NULL,          -- 加密密钥（32字节）
  salt           TEXT NOT NULL,           -- 确定性盐值
  created_at     INTEGER NOT NULL,        -- 密钥创建时间
  updated_at     INTEGER NOT NULL,        -- 密钥更新时间
  FOREIGN KEY (group_id) REFERENCES groups(group_id) ON DELETE CASCADE
);
```

## 使用方法

### 1. 检查群组是否启用加密
```dart
final isEnabled = await GroupEncryptionKeyService.isEncryptionEnabled(groupId);
```

### 2. 获取群组加密密钥
```dart
final key = await GroupEncryptionKeyService.getEncryptionKey(groupId);
```

### 3. 生成私有群组密钥
```dart
final success = await GroupEncryptionKeyService.generatePrivateGroupKey(
  groupId,
  password,
  createdAt,
);
```

### 4. 加密文本消息
```dart
// 使用服务类
final encrypted = await GroupDataEncryption.encryptTextMessage(groupId, message);

// 使用工具类（推荐）
final encrypted = await EncryptionUtils.encryptTextMessage(groupId, message);
```

### 5. 解密文本消息
```dart
// 使用服务类
final decrypted = await GroupDataEncryption.decryptTextMessage(groupId, encrypted);

// 使用工具类（推荐）
final decrypted = await EncryptionUtils.decryptTextMessage(groupId, encrypted);
```

### 6. 加密语音数据
```dart
final encrypted = await EncryptionUtils.encryptVoiceData(groupId, voiceData);
```

### 7. 解密语音数据
```dart
final decrypted = await EncryptionUtils.decryptVoiceData(groupId, encryptedData);
```

## 集成到协议层

在发送数据时：
```dart
// 加密payload
final encryptedPayload = await EncryptionUtils.encryptPayload(groupId, originalPayload);
// 发送加密后的数据（协议帧头不变）
```

在接收数据时：
```dart
// 解密payload
final decryptedPayload = await EncryptionUtils.decryptPayload(groupId, encryptedPayload);
// 处理解密后的数据
```

## 安全特性

1. **密钥隔离：** 不同群组使用不同的密钥
2. **认证加密：** AES-GCM提供机密性和完整性保护
3. **随机IV：** 每次加密使用不同的随机IV
4. **确定性密钥：** 同一群组的所有成员能生成相同密钥
5. **降级策略：** 加密失败时自动降级为明文传输

## 性能考虑

1. **硬件加速：** AES-GCM支持硬件加速
2. **内存管理：** 密钥使用后及时清零
3. **异步操作：** 所有加密操作都是异步的
4. **错误处理：** 完善的异常处理和日志记录

## 测试

运行加密功能演示：
```bash
dart run example/encryption_demo.dart
```

演示包含：
- 密钥生成测试
- 文本消息加密/解密测试
- 语音数据加密/解密测试
- 公共群组密钥测试
- 工具类接口测试

## 依赖

- `pointycastle: ^3.7.3` - 加密算法实现
- `crypto: ^3.0.3` - 哈希算法
- `sqflite: ^2.3.0` - 数据库存储

## 协议层集成

### 发送端加密
- **文本消息**：在`TK8620RequestSender.sendTextMessage`中自动加密文本内容
- **语音数据**：在`TK8620RequestSender.sendVoiceData`和`sendRealTimeVoiceData`中自动加密音频数据
- **加密位置**：只加密payload部分，协议帧头保持不变

### 接收端解密
- **文本消息**：在`TextPacketAssembler.handleTextFrame`中异步解密
- **语音数据**：在`VoicePacketAssembler.handleVoiceFrame`中异步解密
- **播放处理**：在`DiDataDispatcher`中异步解密后播放

### 群组ID获取
- **发送时**：从`ConversationManager.currentConversationId`获取当前群组ID
- **接收时**：从协议帧的`dstId`字段获取群组ID
- **格式转换**：将数字ID转换为8位大写十六进制字符串

## 注意事项

1. **密钥同步：** 确保群组密码在所有成员间一致
2. **数据库迁移：** 升级时会自动创建加密密钥表
3. **向后兼容：** 支持解密旧的未加密数据
4. **错误恢复：** 加密失败时不会阻塞正常通信
5. **异步处理：** 所有加密/解密操作都是异步的，不会阻塞UI线程
